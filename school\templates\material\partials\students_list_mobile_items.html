{% for enrollment in enrollments %}
    <div class="student-item clickable-student-card mdc-ripple-surface"
         data-student-id="{{ enrollment.student.id }}"
         data-enrollment-id="{{ enrollment.id }}"
         data-student-name="{{ enrollment.student.get_full_name }}"
         data-student-level="{{ enrollment.level_fr|default:enrollment.generic_level_fr }}"
         data-student-matricule="{{ enrollment.student.matricule|default:enrollment.student.student_id }}"
         style="cursor: pointer;">
        <div class="student-photo" style="background-image: url('{% if enrollment.student.photo %}{{ enrollment.student.photo.url }}{% elif enrollment.is_second_cycle_fr and enrollment.student.student_id %}{{ enrollment.student.government_photo }}{% else %}{{ enrollment.student.blank_photo }}{% endif %}')"></div>
        <div class="student-info">
            <div class="student-header">
                <div class="student-name">
                    {{ enrollment.student.get_full_name }}
                    <span class="student-id">{{ enrollment.student.student_id|default:"" }}</span>
                </div>
            </div>
            {% if school_education == 'A' %}
                <div class="student-info-row">
                    <span class="info-label">Nom arabe:</span>
                    <span class="info-content">{{ enrollment.student.full_name_ar|default:'-' }}</span>
                </div>
            {% endif %}
            <div class="student-info-row">
                <span class="info-label">Date Nais./Genre:</span>
                <span class="info-content">
                    {{ enrollment.student.birth_date_str|default:"-" }} <span class='dot-seperator'>•</span> {{ enrollment.student.get_gender_display|default:"-" }}
                </span>
            </div>
            <div class="student-info-row">
                <span class="info-label">Classe:</span>
                <span class="info-content level-info">
                    {% if school_education != 'F' %}
                        <span class="level-label">Fran:</span> {{ enrollment.level_fr|default:enrollment.generic_level_fr|default:"-" }} <span class='dot-seperator'>•</span>
                        <span class="level-label">Ar:</span> {{ enrollment.level_ar|default:enrollment.generic_level_ar|default:"-" }}
                    {% else %}
                        {{ enrollment.level_fr|default:enrollment.generic_level_fr|default:"-" }}
                    {% endif %}
                </span>
            </div>
            <div class="student-info-row">
                <span class="info-label">Paiement:</span>
                <span class="info-content payment-info">
                    <span class="payment-label">Payé:</span> {{ enrollment.paid|floatformat:0|default:"-" }} <span class='dot-seperator'>•</span>
                    <span class="payment-label">Reste:</span> {% if enrollment.remaining == 0 and enrollment.amount > 0 %}Soldé{% else %}{{ enrollment.remaining|floatformat:0|default:"-" }}{% endif %}
                </span>
            </div>
        </div>
        <!-- <div class="student-actions">
            <button class="edit-btn" title="Modifier l'élève"
                    hx-get="{% url 'school:student_edit' enrollment.pk %}"
                    hx-target="#dialog">
                <span class="material-icons">edit</span>
            </button>
            <button class="view-btn" title="Voir détails"
                    hx-get="{% url 'school:student_detail' enrollment.pk %}"
                    hx-target="#app-content">
                <span class="material-icons">visibility</span>
            </button>
        </div> -->
    </div>
{% endfor %}
